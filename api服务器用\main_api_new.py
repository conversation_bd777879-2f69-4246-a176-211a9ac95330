import pandas as pd
import os
from flask import Flask, request, jsonify
from ultralytics import YOLO
from PIL import Image
import base64
from collections import defaultdict

app = Flask(__name__)

# 清空result文件夹
def clear_result_folder():
    folder_path = "result"
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if os.path.isfile(file_path):
            os.remove(file_path)

    img_folder = "test_data"
    for filename in os.listdir(img_folder):
        file_path = os.path.join(img_folder, filename)
        if os.path.isfile(file_path):
            os.remove(file_path)

# 第一次识别
def pred(model, folder_path):
    results = model.predict(source=folder_path)
    result_list = []

    # 处理每个预测结果
    for i, result in enumerate(results):
        image_path = result.path
        image_name = os.path.basename(image_path)  # 提取文件名部分
        boxes = result.boxes.xyxy.tolist()
        labels = result.boxes.cls.tolist()

        # 添加每个标注的信息到结果列表
        for label, box in zip(labels, boxes):
            xmin, ymin, xmax, ymax = box
            result_list.append({
                "image": image_name,
                "label": label,
                "xmin": xmin,
                "ymin": ymin,
                "xmax": xmax,
                "ymax": ymax
            })

        print(f"处理完成预测结果 {i+1}/{len(results)}")

    print("初步预测结果已生成")
    return result_list

# 修正吊臂下的人
def getUnder(df):
    # 创建一个新的DataFrame用于存储修正后的结果
    new_df = df.copy()

    # 遍历每一张图片
    for image in df['image'].unique():
        # 获取当前图片的标注数据
        image_data = df[df['image'] == image]

        # 查找标注为0的数据
        label_0_data = image_data[image_data['label'] == 0]

        # 查找标注为3的数据
        label_3_data = image_data[image_data['label'] == 3]

        # 检查是否满足条件并修改标注
        if len(label_0_data) > 0 and len(label_3_data) > 0:
            for index_3, row_3 in label_3_data.iterrows():
                xmin_3 = row_3['xmin']
                ymin_3 = row_3['ymin']
                xmax_3 = row_3['xmax']
                ymax_2 = row_3['ymax']

                for index_0, row_0 in label_0_data.iterrows():
                    xmin_0 = row_0['xmin']
                    ymin_0 = row_0['ymin']
                    xmax_0 = row_0['xmax']
                    ymax_0 = row_0['ymax']

                    if ((xmin_3 >= xmin_0 and xmin_3 <= xmax_0) or (xmax_3 >= xmin_0 and xmax_3 <= xmax_0)):
                        new_df.loc[index_3, 'label'] = 2
                        break  # 如果找到匹配的标注0框，就跳出内层循环

    print("标注修正已完成")
    return new_df

# 二次修正，根据图片深度近大远小修正其他人
def getOther(df):
    # 创建一个字典，用于存储每张图片中的所有标注框
    image_boxes = {}

    # 遍历数据框中的每一行
    for index, row in df.iterrows():
        image = row['image']
        label = row['label']
        xmin = row['xmin']
        ymin = row['ymin']
        xmax = row['xmax']
        ymax = row['ymax']

        # 如果图片不在字典中，则将其添加到字典，并初始化一个空的标注框列表
        if image not in image_boxes:
            image_boxes[image] = []

        # 将当前标注框添加到对应图片的标注框列表中
        image_boxes[image].append({
            'label': label,
            'xmin': xmin,
            'ymin': ymin,
            'xmax': xmax,
            'ymax': ymax
        })

    # 遍历图片和标注框字典中的每个键值对
    for image, boxes in image_boxes.items():
        # 统计标注框中 label 为 2 的数量
        label_2_count = sum(box['label'] == 2 for box in boxes)

        # 如果标注框中 label 2 的数量大于等于 2
        if label_2_count >= 2:
            max_area = 0
            max_area_index = -1

            for i, box in enumerate(boxes):
                if box['label'] == 2:
                    area = (box['xmax'] - box['xmin']) * (box['ymax'] - box['ymin'])
                    if area > max_area:
                        max_area = area
                        max_area_index = i

            for i, box in enumerate(boxes):
                if box['label'] == 2 and i != max_area_index:
                    box['label'] = 3

    # 将修改后的标注框重新整理为数据框
    new_df = pd.DataFrame([{
        'image': image,
        'label': box['label'],
        'xmin': box['xmin'],
        'ymin': box['ymin'],
        'xmax': box['xmax'],
        'ymax': box['ymax']
    } for image, boxes in image_boxes.items() for box in boxes])

    print('标注二次修正已完成')
    return new_df


def calculate_iou(box1, box2):
    # 这是一个假设的 IoU 计算函数，你需要根据你的需求实现它
    # box1 和 box2 都是形如 [xmin, ymin, xmax, ymax] 的列表
    x1_inter, y1_inter = max(box1[0], box2[0]), max(box1[1], box2[1])
    x2_inter, y2_inter = min(box1[2], box2[2]), min(box1[3], box2[3])
    inter_area = max(0, x2_inter - x1_inter + 1) * max(0, y2_inter - y1_inter + 1)
    box1_area = (box1[2] - box1[0] + 1) * (box1[3] - box1[1] + 1)
    box2_area = (box2[2] - box2[0] + 1) * (box2[3] - box2[1] + 1)
    iou = inter_area / float(box1_area + box2_area - inter_area)
    return iou


def filter_boxes_by_iou(df, iou_threshold=0.5):
    # 使用defaultdict来存储每张图片的边界框
    boxes_per_image = defaultdict(list)

    # 从 DataFrame 中提取数据
    for _, row in df.iterrows():
        image_name, label, xmin, ymin, xmax, ymax = row
        xmin, ymin, xmax, ymax = float(xmin), float(ymin), float(xmax), float(ymax)
        box = [xmin, ymin, xmax, ymax]
        boxes_per_image[(image_name, label)].append(box)

    # 对每张图片的每个label的边界框进行IoU计算和筛选
    filtered_boxes = []
    for (image_name, label), boxes in boxes_per_image.items():
        # 如果边界框数量小于2，则全部保留
        if len(boxes) < 2:
            filtered_boxes.extend([(image_name, label, *box) for box in boxes])
            continue

        # 对边界框进行排序，这有助于后续的IoU计算和筛选
        sorted_boxes = sorted(boxes, key=lambda x: (x[0], x[1], -x[2], -x[3]))

        # 使用贪心算法或NMS（非极大值抑制）来保留IoU低于阈值的边界框
        keep = []
        for i, box_a in enumerate(sorted_boxes):
            if box_a in keep:
                continue

            # 检查边界框的高度是否小于宽度的4倍
            width_a = box_a[2] - box_a[0]
            height_a = box_a[3] - box_a[1]
            if height_a < 4 * width_a:  # 如果满足条件
                for box_b in sorted_boxes[i + 1:]:
                    if calculate_iou(box_a, box_b) > iou_threshold:
                        break
                else:
                    keep.append(box_a)  # 只有当不满足IoU阈值时才添加到keep中

        filtered_boxes.extend([(image_name, label, *box) for box in keep])

    # 将结果写入新的CSV文件或返回DataFrame
    result_df = pd.DataFrame(filtered_boxes, columns=["image", "label", "xmin", "ymin", "xmax", "ymax"])
    # result_df.to_csv(output_file, index=False)
    # 如果你不想直接写入文件，可以返回DataFrame
    return result_df

    # 使用函数



# 排序表格
def order(df):
    # 对每张图片内部的目标进行排序
    sorted_df = df.groupby('image').apply(lambda x: x.sort_values(['xmin', 'ymin'])).reset_index(drop=True)

    print("排序已完成")
    return sorted_df

# 入口函数
@app.route("/AI/competition", methods=["POST"])
def handle_competition():
    # 清空result文件夹
    clear_result_folder()

    # 获取base64编码的图片数据
    json_data = request.get_json()
    base64code = json_data.get("base64code")

    # 将base64编码转换为图片文件
    image_data = base64.b64decode(base64code)
    image_path = "test_data/temp_image.jpg"
    with open(image_path, "wb") as file:
        file.write(image_data)

    # 进行预测和修正操作
    folder_path = "test_data"   # 待推理图片
    results = pred(model=model, folder_path=folder_path)

    df = pd.DataFrame(results)
    df1 = filter_boxes_by_iou(df=df)
    final_df = order(df=df1)

    print("已调整submission表格顺序")

    # 构造返回结果
    results = []
    for _, row in final_df.iterrows():
        results.append({
            "label": int(row["label"]),
            "xmin": int(row["xmin"]),
            "ymin": int(row["ymin"]),
            "xmax": int(row["xmax"]),
            "ymax": int(row["ymax"])
        })
    response = {
        "status": 1,
        "results": results
    }
    return jsonify(response)

if __name__ == '__main__':
    model = YOLO("models/best.pt")
    app.run(host="0.0.0.0", port=28000)

