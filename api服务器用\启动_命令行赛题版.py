import csv
import pandas as pd
import os
from ultralytics import YOLO
from PIL import Image, ImageDraw,ImageFont
import time
from collections import defaultdict

#第一次识别
def pred(model,folder_path,csv_file):
    results = model.predict(source=folder_path)

    # 创建CSV文件并写入表头

    with open(csv_file, "w", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)
        writer.writerow(["image", "label", "xmin", "ymin", "xmax", "ymax"])

        # 处理每个预测结果
        for i, result in enumerate(results):
            image_path = result.path
            image_name = os.path.basename(image_path)  # 提取文件名部分
            boxes = result.boxes.xyxy.tolist()
            labels = result.boxes.cls.tolist()

            # 写入每个标注的信息
            for label, box in zip(labels, boxes):
                xmin, ymin, xmax, ymax = box
                writer.writerow([image_name, label, xmin, ymin, xmax, ymax])

            print(f"处理完成预测结果 {i+1}/{len(results)}")

    print(f"初步预测结果已保存到 {csv_file}")

# 计算同一张图片的IOU
def calculate_iou(box1, box2):
    """
    计算两个边界框的IoU
    box1, box2: [xmin, ymin, xmax, ymax]
    """
    # 计算交集的坐标
    xi1 = max(box1[0], box2[0])
    yi1 = max(box1[1], box2[1])
    xi2 = min(box1[2], box2[2])
    yi2 = min(box1[3], box2[3])

    # 计算交集面积
    inter_area = max(xi2 - xi1, 0) * max(yi2 - yi1, 0)

    # 计算两个边界框的面积
    box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
    box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])

    # 计算并集面积
    union_area = box1_area + box2_area - inter_area

    # 计算IoU
    iou = inter_area / union_area
    return iou

#过滤多余IOU
def filter_boxes_by_iou(csv_file, output_file, iou_threshold=0.5):
    # 使用defaultdict来存储每张图片的边界框
    boxes_per_image = defaultdict(list)

    # 读取CSV文件
    with open(csv_file, 'r', newline='') as file:
        reader = csv.reader(file)
        next(reader)  # 跳过表头
        for row in reader:
            image_name, label, xmin, ymin, xmax, ymax = row
            xmin, ymin, xmax, ymax = float(xmin), float(ymin), float(xmax), float(ymax)
            box = [xmin, ymin, xmax, ymax]
            boxes_per_image[(image_name, label)].append(box)

            # 对每张图片的每个label的边界框进行IoU计算和筛选
    filtered_boxes = []
    for (image_name, label), boxes in boxes_per_image.items():
        # 如果边界框数量小于2，则全部保留
        if len(boxes) < 2:
            filtered_boxes.extend([(image_name, label, *box) for box in boxes])
            continue

            # 对边界框进行排序，这有助于后续的IoU计算和筛选
        sorted_boxes = sorted(boxes, key=lambda x: (x[0], x[1], -x[2], -x[3]))

        # 使用贪心算法或NMS（非极大值抑制）来保留IoU低于阈值的边界框
        keep = []
        for i, box_a in enumerate(sorted_boxes):
            if box_a in keep:
                continue

                # 检查边界框的高度是否小于宽度的4倍
            width_a = box_a[2] - box_a[0]
            height_a = box_a[3] - box_a[1]
            if height_a < 4 * width_a:  # 如果满足条件
                for box_b in sorted_boxes[i + 1:]:
                    if calculate_iou(box_a, box_b) > iou_threshold:
                        break
                else:
                    keep.append(box_a)  # 只有当不满足IoU阈值时才添加到keep中

        filtered_boxes.extend([(image_name, label, *box) for box in keep])

        # 将结果写入新的CSV文件
    with open(output_file, 'w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(["image", "label", "xmin", "ymin", "xmax", "ymax"])
        writer.writerows(filtered_boxes)

    # 使用函数

#合成图片
def comb(folder_path):
    # 定义标签与颜色的映射关系
    label_colors = {
        0: ('sly', 'red'),
    }

    # 读取标注信息
    annotations = {}
    with open(folder_path, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            image_path = row['image']
            if image_path not in annotations:
                annotations[image_path] = []
            annotations[image_path].append(row)

    # 遍历标注信息
    for image_path, image_annotations in annotations.items():
        # 打开图像
        image = Image.open('Test_Imgs/' + image_path)

        # 创建绘图对象
        draw = ImageDraw.Draw(image)

        # 遍历每个标注
        for annotation in image_annotations:
            label = int(float(annotation['label']))
            xmin = float(annotation['xmin'])
            ymin = float(annotation['ymin'])
            xmax = float(annotation['xmax'])
            ymax = float(annotation['ymax'])

            # 计算边界框的大小
            bbox_width = xmax - xmin
            bbox_height = ymax - ymin
            # 选择宽或高中的较小值作为字体大小的参考
            bbox_size = min(bbox_width, bbox_height)

            # 获取标签对应的文本和颜色
            label_text, label_color = label_colors[label]

            # 绘制矩形框
            line_width = 5  # 设置线条宽度
            draw.rectangle([(xmin, ymin), (xmax, ymax)], outline=label_color, width=line_width)

            # 绘制标签
            font = ImageFont.truetype('Font/platech.ttf', bbox_size*0.2)
            draw.text((xmin, ymin), label_text, fill=label_color,font=font)

        # 保存标注后的图像
        image.save('results/annotated_' + image_path)

        # 关闭图像
        image.close()
        print('合成图像已完成，结果保存到results')
#排序表格
def order(csv_file,out_csv):
    # 读取CSV文件
    with open(csv_file, 'r') as file:
        reader = csv.reader(file)
        header = next(reader)  # 读取表头

        # 将CSV数据存储到字典中，以图片名称作为键，目标列表作为值
        data = {}
        for row in reader:
            image = row[0]
            if image not in data:
                data[image] = []
            data[image].append(row)

    # 对每张图片内部的目标进行排序
    for image in data:
        data[image] = sorted(data[image], key=lambda x: (float(x[2]), float(x[3])))

    # 将排序后的数据写入新的CSV文件
    sorted_csv_file = out_csv
    with open(sorted_csv_file, 'w', newline='', encoding='utf-8') as file:
        writer = csv.writer(file)
        writer.writerow(header)  # 写入表头
        for image in data:
            writer.writerows(data[image])

    print(f"排序后的数据已保存到文件: {sorted_csv_file}")
#入口函数
if __name__ == '__main__':

    model = YOLO("models/best.pt")
    folder_path = "Test_Imgs"   #待推理图片
    csv_file = "results/pred.csv"
    #初步推理
    pred(model=model,folder_path=folder_path,csv_file=csv_file)
    #过滤重叠
    filtered_file = "results/filtered_pred.csv"
    filter_boxes_by_iou(csv_file, filtered_file)

    #对表格排序
    final_csv = 'submission.csv'
    order(filtered_file,final_csv)
    print("已调整submission表格顺序")

    #合成标注与图片
    comb(folder_path=final_csv)

    print("运行结束，感谢使用")
    print("程序开发：缙云县供电公司 韩剑")