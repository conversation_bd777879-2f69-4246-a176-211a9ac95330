import pandas as pd
import os
from flask import Flask, request, jsonify
from ultralytics import YOLO
from PIL import Image
import base64

app = Flask(__name__)

# 清空result文件夹
def clear_result_folder():
    folder_path = "result"
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if os.path.isfile(file_path):
            os.remove(file_path)

    img_folder = "test_data"
    for filename in os.listdir(img_folder):
        file_path = os.path.join(img_folder, filename)
        if os.path.isfile(file_path):
            os.remove(file_path)

# 第一次识别
def pred(model, folder_path):
    results = model.predict(source=folder_path)
    result_list = []

    # 处理每个预测结果
    for i, result in enumerate(results):
        image_path = result.path
        image_name = os.path.basename(image_path)  # 提取文件名部分
        boxes = result.boxes.xyxy.tolist()
        labels = result.boxes.cls.tolist()

        # 添加每个标注的信息到结果列表
        for label, box in zip(labels, boxes):
            xmin, ymin, xmax, ymax = box
            result_list.append({
                "image": image_name,
                "label": label,
                "xmin": xmin,
                "ymin": ymin,
                "xmax": xmax,
                "ymax": ymax
            })

        print(f"处理完成预测结果 {i+1}/{len(results)}")

    print("初步预测结果已生成")
    return result_list

# 修正吊臂下的人
def getUnder(df):
    # 创建一个新的DataFrame用于存储修正后的结果
    new_df = df.copy()

    # 遍历每一张图片
    for image in df['image'].unique():
        # 获取当前图片的标注数据
        image_data = df[df['image'] == image]

        # 查找标注为0的数据
        label_0_data = image_data[image_data['label'] == 0]

        # 查找标注为3的数据
        label_3_data = image_data[image_data['label'] == 3]

        # 检查是否满足条件并修改标注
        if len(label_0_data) > 0 and len(label_3_data) > 0:
            for index_3, row_3 in label_3_data.iterrows():
                xmin_3 = row_3['xmin']
                ymin_3 = row_3['ymin']
                xmax_3 = row_3['xmax']
                ymax_2 = row_3['ymax']

                for index_0, row_0 in label_0_data.iterrows():
                    xmin_0 = row_0['xmin']
                    ymin_0 = row_0['ymin']
                    xmax_0 = row_0['xmax']
                    ymax_0 = row_0['ymax']

                    if ((xmin_3 >= xmin_0 and xmin_3 <= xmax_0) or (xmax_3 >= xmin_0 and xmax_3 <= xmax_0)):
                        new_df.loc[index_3, 'label'] = 2
                        break  # 如果找到匹配的标注0框，就跳出内层循环

    print("标注修正已完成")
    return new_df

# 二次修正，根据图片深度近大远小修正其他人
def getOther(df):
    # 创建一个字典，用于存储每张图片中的所有标注框
    image_boxes = {}

    # 遍历数据框中的每一行
    for index, row in df.iterrows():
        image = row['image']
        label = row['label']
        xmin = row['xmin']
        ymin = row['ymin']
        xmax = row['xmax']
        ymax = row['ymax']

        # 如果图片不在字典中，则将其添加到字典，并初始化一个空的标注框列表
        if image not in image_boxes:
            image_boxes[image] = []

        # 将当前标注框添加到对应图片的标注框列表中
        image_boxes[image].append({
            'label': label,
            'xmin': xmin,
            'ymin': ymin,
            'xmax': xmax,
            'ymax': ymax
        })

    # 遍历图片和标注框字典中的每个键值对
    for image, boxes in image_boxes.items():
        # 统计标注框中 label 为 2 的数量
        label_2_count = sum(box['label'] == 2 for box in boxes)

        # 如果标注框中 label 2 的数量大于等于 2
        if label_2_count >= 2:
            max_area = 0
            max_area_index = -1

            for i, box in enumerate(boxes):
                if box['label'] == 2:
                    area = (box['xmax'] - box['xmin']) * (box['ymax'] - box['ymin'])
                    if area > max_area:
                        max_area = area
                        max_area_index = i

            for i, box in enumerate(boxes):
                if box['label'] == 2 and i != max_area_index:
                    box['label'] = 3

    # 将修改后的标注框重新整理为数据框
    new_df = pd.DataFrame([{
        'image': image,
        'label': box['label'],
        'xmin': box['xmin'],
        'ymin': box['ymin'],
        'xmax': box['xmax'],
        'ymax': box['ymax']
    } for image, boxes in image_boxes.items() for box in boxes])

    print('标注二次修正已完成')
    return new_df

# 排序表格
def order(df):
    # 对每张图片内部的目标进行排序
    sorted_df = df.groupby('image').apply(lambda x: x.sort_values(['xmin', 'ymin'])).reset_index(drop=True)

    print("排序已完成")
    return sorted_df

# 入口函数
@app.route("/AI/competition", methods=["POST"])
def handle_competition():
    # 清空result文件夹
    clear_result_folder()

    # 获取base64编码的图片数据
    json_data = request.get_json()
    base64code = json_data.get("base64code")

    # 将base64编码转换为图片文件
    image_data = base64.b64decode(base64code)
    image_path = "test_data/temp_image.jpg"
    with open(image_path, "wb") as file:
        file.write(image_data)

    # 进行预测和修正操作
    folder_path = "test_data"   # 待推理图片
    results = pred(model=model, folder_path=folder_path)

    df = pd.DataFrame(results)
    df1 = getUnder(df=df)
    df2 = getOther(df=df1)
    final_df = order(df=df2)

    print("已调整submission表格顺序")

    # 构造返回结果
    results = []
    for _, row in final_df.iterrows():
        results.append({
            "label": int(row["label"]),
            "xmin": int(row["xmin"]),
            "ymin": int(row["ymin"]),
            "xmax": int(row["xmax"]),
            "ymax": int(row["ymax"])
        })
    response = {
        "status": 1,
        "results": results
    }
    return jsonify(response)

if __name__ == '__main__':
    model = YOLO("best.pt")
    app.run(host="0.0.0.0", port=28000)

