<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1250</width>
    <height>830</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1250</width>
    <height>830</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1250</width>
    <height>830</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>变电站现场渗漏油检测程序</string>
  </property>
  <property name="windowIcon">
   <iconset resource="ui_sources.qrc">
    <normaloff>:/icons/ui_imgs/icons/目标检测.png</normaloff>:/icons/ui_imgs/icons/目标检测.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QFrame" name="frame">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>100</y>
      <width>791</width>
      <height>711</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QFrame" name="frame_2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>0</y>
       <width>771</width>
       <height>481</height>
      </rect>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <widget class="QLabel" name="label_show">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>770</width>
        <height>480</height>
       </rect>
      </property>
      <property name="minimumSize">
       <size>
        <width>770</width>
        <height>480</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>770</width>
        <height>480</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">border-image: url(:/icons/ui_imgs/2.png);</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </widget>
    <widget class="QFrame" name="frame_3">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>480</y>
       <width>771</width>
       <height>221</height>
      </rect>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <widget class="QGroupBox" name="groupBox_3">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>10</y>
        <width>771</width>
        <height>221</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>华文楷体</family>
        <pointsize>16</pointsize>
       </font>
      </property>
      <property name="title">
       <string>检测结果与位置信息</string>
      </property>
      <widget class="QTableWidget" name="tableWidget">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>30</y>
         <width>751</width>
         <height>181</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>华文楷体</family>
         <pointsize>14</pointsize>
        </font>
       </property>
       <column>
        <property name="text">
         <string>序号</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>文件路径</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>类别</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>置信度</string>
        </property>
       </column>
       <column>
        <property name="text">
         <string>坐标位置</string>
        </property>
       </column>
      </widget>
     </widget>
    </widget>
   </widget>
   <widget class="QFrame" name="frame_4">
    <property name="geometry">
     <rect>
      <x>810</x>
      <y>100</y>
      <width>431</width>
      <height>711</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QGroupBox" name="groupBox">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>431</width>
       <height>171</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>文件导入</string>
     </property>
     <widget class="QLineEdit" name="PiclineEdit">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>40</y>
        <width>311</width>
        <height>31</height>
       </rect>
      </property>
      <property name="inputMask">
       <string/>
      </property>
      <property name="placeholderText">
       <string>请选择图片文件</string>
      </property>
     </widget>
     <widget class="QLineEdit" name="VideolineEdit">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>80</y>
        <width>311</width>
        <height>31</height>
       </rect>
      </property>
      <property name="placeholderText">
       <string>请选择视频文件</string>
      </property>
     </widget>
     <widget class="QPushButton" name="CapBtn">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>120</y>
        <width>30</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border-image: url(:/icons/ui_imgs/icons/camera.png);</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="PicBtn">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>40</y>
        <width>30</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border-image: url(:/icons/ui_imgs/icons/img.png);</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QPushButton" name="VideoBtn">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>80</y>
        <width>30</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border-image: url(:/icons/ui_imgs/icons/video.png);</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
     <widget class="QLineEdit" name="CaplineEdit">
      <property name="geometry">
       <rect>
        <x>70</x>
        <y>120</y>
        <width>311</width>
        <height>31</height>
       </rect>
      </property>
      <property name="placeholderText">
       <string>摄像头未开启</string>
      </property>
     </widget>
     <widget class="QPushButton" name="FilesBtn">
      <property name="geometry">
       <rect>
        <x>390</x>
        <y>40</y>
        <width>30</width>
        <height>30</height>
       </rect>
      </property>
      <property name="styleSheet">
       <string notr="true">border-image: url(:/icons/ui_imgs/icons/folder.png);</string>
      </property>
      <property name="text">
       <string/>
      </property>
     </widget>
    </widget>
    <widget class="QGroupBox" name="groupBox_2">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>180</y>
       <width>431</width>
       <height>371</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>检测结果</string>
     </property>
     <widget class="QFrame" name="frame_6">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>200</y>
        <width>431</width>
        <height>171</height>
       </rect>
      </property>
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <property name="frameShadow">
       <enum>QFrame::Raised</enum>
      </property>
      <widget class="QLabel" name="label_4">
       <property name="geometry">
        <rect>
         <x>10</x>
         <y>10</y>
         <width>131</width>
         <height>41</height>
        </rect>
       </property>
       <property name="font">
        <font>
         <family>华文楷体</family>
         <pointsize>16</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>目标位置：</string>
       </property>
      </widget>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>60</y>
         <width>161</width>
         <height>37</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="label_6">
          <property name="font">
           <font>
            <family>华文楷体</family>
            <pointsize>16</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;xmin:&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_xmin">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>120</red>
                <green>120</green>
                <blue>120</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>60</y>
         <width>161</width>
         <height>37</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label_8">
          <property name="text">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;ymin：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_ymin">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>120</red>
                <green>120</green>
                <blue>120</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>20</x>
         <y>120</y>
         <width>161</width>
         <height>37</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="label_7">
          <property name="text">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;xmax：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_xmax">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>120</red>
                <green>120</green>
                <blue>120</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>210</x>
         <y>120</y>
         <width>161</width>
         <height>37</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLabel" name="label_9">
          <property name="text">
           <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;ymax：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_ymax">
          <property name="palette">
           <palette>
            <active>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </active>
            <inactive>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>255</red>
                <green>0</green>
                <blue>0</blue>
               </color>
              </brush>
             </colorrole>
            </inactive>
            <disabled>
             <colorrole role="WindowText">
              <brush brushstyle="SolidPattern">
               <color alpha="255">
                <red>120</red>
                <green>120</green>
                <blue>120</blue>
               </color>
              </brush>
             </colorrole>
            </disabled>
           </palette>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>208</x>
        <y>40</y>
        <width>211</width>
        <height>37</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;目标数目：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_nums">
         <property name="palette">
          <palette>
           <active>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>90</y>
        <width>291</width>
        <height>38</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <widget class="QLabel" name="label_5">
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;目标选择：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QComboBox" name="comboBox"/>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget_2">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>40</y>
        <width>171</width>
        <height>37</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_7">
       <item>
        <widget class="QLabel" name="label_10">
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;用时：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="time_lb">
         <property name="palette">
          <palette>
           <active>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="Text">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
            <colorrole role="PlaceholderText">
             <brush brushstyle="SolidPattern">
              <color alpha="128">
               <red>0</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>140</y>
        <width>191</width>
        <height>41</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_8">
       <item>
        <widget class="QLabel" name="label_11">
         <property name="text">
          <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;&lt;span style=&quot; font-weight:600;&quot;&gt;置信度：&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_conf">
         <property name="palette">
          <palette>
           <active>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </active>
           <inactive>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>255</red>
               <green>0</green>
               <blue>0</blue>
              </color>
             </brush>
            </colorrole>
           </inactive>
           <disabled>
            <colorrole role="WindowText">
             <brush brushstyle="SolidPattern">
              <color alpha="255">
               <red>120</red>
               <green>120</green>
               <blue>120</blue>
              </color>
             </brush>
            </colorrole>
           </disabled>
          </palette>
         </property>
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QGroupBox" name="groupBox_4">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>560</y>
       <width>431</width>
       <height>141</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>16</pointsize>
      </font>
     </property>
     <property name="title">
      <string>操作</string>
     </property>
     <widget class="QPushButton" name="SaveBtn">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>50</y>
        <width>151</width>
        <height>51</height>
       </rect>
      </property>
      <property name="text">
       <string>保存</string>
      </property>
      <property name="icon">
       <iconset resource="ui_sources.qrc">
        <normaloff>:/icons/ui_imgs/icons/保存.png</normaloff>:/icons/ui_imgs/icons/保存.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>30</width>
        <height>30</height>
       </size>
      </property>
     </widget>
     <widget class="QPushButton" name="ExitBtn">
      <property name="geometry">
       <rect>
        <x>250</x>
        <y>50</y>
        <width>151</width>
        <height>51</height>
       </rect>
      </property>
      <property name="text">
       <string>退出</string>
      </property>
      <property name="icon">
       <iconset resource="ui_sources.qrc">
        <normaloff>:/icons/ui_imgs/icons/退出.png</normaloff>:/icons/ui_imgs/icons/退出.png</iconset>
      </property>
      <property name="iconSize">
       <size>
        <width>30</width>
        <height>30</height>
       </size>
      </property>
     </widget>
    </widget>
   </widget>
   <widget class="QFrame" name="frame_5">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>10</y>
      <width>1231</width>
      <height>91</height>
     </rect>
    </property>
    <property name="frameShape">
     <enum>QFrame::StyledPanel</enum>
    </property>
    <property name="frameShadow">
     <enum>QFrame::Raised</enum>
    </property>
    <widget class="QLabel" name="label_3">
     <property name="geometry">
      <rect>
       <x>230</x>
       <y>0</y>
       <width>741</width>
       <height>51</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>30</pointsize>
      </font>
     </property>
     <property name="text">
      <string>变电站现场渗漏油检测程序</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_2">
     <property name="geometry">
      <rect>
       <x>20</x>
       <y>60</y>
       <width>311</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>14</pointsize>
       <underline>true</underline>
      </font>
     </property>
     <property name="text">
      <string>国网缙云县供电公司</string>
     </property>
    </widget>
    <widget class="QLabel" name="label_12">
     <property name="geometry">
      <rect>
       <x>1070</x>
       <y>60</y>
       <width>131</width>
       <height>21</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>华文楷体</family>
       <pointsize>14</pointsize>
       <underline>true</underline>
      </font>
     </property>
     <property name="text">
      <string>作者：韩剑</string>
     </property>
    </widget>
   </widget>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources>
  <include location="ui_sources.qrc"/>
 </resources>
 <connections/>
</ui>
