# -*- coding: UTF-8 -*-
import os
import requests
import base64
import json
import time
import pandas as pd
import traceback
import argparse


# base64编码
def to_base64(input_path):
    with open(input_path, "rb") as f:
        base64_str = base64.b64encode(f.read())
    return str(base64_str, encoding="utf-8")


# 解析api接口返回
def api_ret_parse(result):
    label_results = None
    ret = False
    try:
        status = result.get("status")
        if status == 1:
            label_results = result.get("results", [])
            label_results = pd.DataFrame(label_results)
            ret = True
        else:
            print("status is not 1")
            print("result:{}".format(result))
    except Exception as e:
        print("api_ret_parse fail")
        print(str(e))
        print(str(traceback.format_exc()))
        print("result:{}".format(result))
    return ret, label_results


# post请求接收失败时，自动重新发送，循环发送3次
def post_retry(url, send_json, headers={"Content-Type": "application/json"}, timeout=(3, 60), retry_cnt=3):
    ret = (True, "success", None)
    for req_cnt in range(retry_cnt):
        ret_b, err_msg, result = post(url, send_json, headers=headers, timeout=timeout)
        if ret_b:
            ret = (ret_b, err_msg, result)
            break
        else:
            print("try req_cnt:{}/{} fail, err_msg:{}".format(req_cnt + 1, retry_cnt, err_msg))
            ret = (ret_b, err_msg, result)
    return ret


# 发送post请求
def post(url, send_json, headers={"Content-Type": "application/json"}, timeout=(3, 60)):
    try:
        response = requests.post(url=url, headers=headers, json=send_json, timeout=timeout)
        # print(response.text)
        if response.status_code != 200:
            # # from bs4 import BeautifulSoup
            # soup = BeautifulSoup(response.content)
            # ret = (False, "status code error:{},{}".format(response.status_code, soup.p),None)
            ret = (False, "status code error:{},{}".format(response.status_code, response.text), None)
        else:
            rec_json = json.loads(response.text)
            ret = (True, "success", rec_json)
    except requests.exceptions.ConnectTimeout as e:
        ret = (False, "connect timeout:{} seconds, e:{}".format(timeout, e), None)
    except requests.exceptions.ReadTimeout as e:
        ret = (False, "read timeout:{} seconds, e:{}".format(timeout, e), None)
    except requests.exceptions.ConnectionError as e:
        ret = (False, "connect error, e:{}".format(e), None)
    except Exception as e:
        print(str(e))
        print(traceback.format_exc())
        ret = (False, "other connect error, e:{}".format(e), None)
    return ret


# 单张图片的远程推理
def get_submission_single(url, filepath):
    try:
        # base64编码
        base64_str = to_base64(filepath)
        send_json = {"base64code": base64_str}
        # 发送api请求
        # ret, reason, result = post(url, send_json)
        ret, reason, result = post_retry(url, send_json, retry_cnt = REQ_RETRY_CNT)
        if not ret:
            print("post error reason:{}".format(reason))
            return ret, reason, None
        # 解析api返回结果
        ret, label_result = api_ret_parse(result)
        if not ret:
            print("api_ret_parse error")
            return ret, "api_ret_parse error", None
        else:
            return ret, "success", label_result
    except Exception as e:
        print(str(e))
        print(traceback.format_exc())
        return False, "unknow reason", None


# 每张图片的推理状态统计描述
def run_status_results_describe(run_status_results):
    runtime_success = run_status_results.query("status==True")
    runtime_fails = run_status_results.query("status==False")
    # runtime_timeout = run_status_results.query("status.isnull()")
    runtime_timeout = run_status_results.loc[run_status_results.status.isnull()]
    all_image_cnt = run_status_results.shape[0]
    success_image_cnt = runtime_success.shape[0]
    fail_image_cnt = runtime_fails.shape[0]
    timeout_image_cnt = runtime_timeout.shape[0]
    print("all:{},success:{},fail:{},timeout:{}".format(all_image_cnt, success_image_cnt, fail_image_cnt,
                                                        timeout_image_cnt))


# api请求得到图片标注结果
def get_submission_iter(url, image_folder):
    image_ids = os.listdir(image_folder)
    if len(image_ids)==0:
        print("no image in {}".format(image_folder))
        return False, None, None
    label_results = []
    run_status_results = []
    start_time = time.time()
    last_time = start_time
    # 遍历每一张图片
    for i, image_id in enumerate(image_ids):
        # print("image_id:{}".format(image_id))
        filepath = os.path.join(image_folder, image_id)
        ret, context, label_result = get_submission_single(url, filepath)
        # 运行时间统计
        this_time = time.time()
        eval_runtime = round(this_time - start_time, 2)
        image_runtime = round(this_time - last_time, 2)
        last_time = this_time
        runtime_result = [image_id, image_runtime, ret, context]
        runtime_result = pd.DataFrame(runtime_result).T
        runtime_result.columns = ["image", "runtime", "status", "context"]
        run_status_results.append(runtime_result)
        # 若图片推理成功，则存储推理结果
        if ret:
            print("{}/{}, image:{} ,runtime:{} ,infer success".format(i+1, len(image_ids), image_id, image_runtime))
            if not label_result.empty:
                # 仅仅在解析的结果不为空时，将标注结果加入列表，
                label_result["image"] = image_id
                label_results.append(label_result)
            else:
                # 若解析得到的图片标注为空，则打印
                print("{}/{}, image:{} results has no label".format(i+1, len(image_ids), image_id))
        else:
            print("{}/{}, image:{} ,runtime:{} ,infer fail, context:{}".format(i+1, len(image_ids), image_id, image_runtime, context))
        if eval_runtime >= EVAL_RUNTIME_LIMIT:
            print("get_submission_iter timeout, runtime: {} s over limit:{}".format(eval_runtime, EVAL_RUNTIME_LIMIT))
            break

    # 所有图片结果合并
    if label_results:
        label_results = pd.concat(label_results)
    else:
        label_results = pd.DataFrame(columns=["image", "label", "xmin", "ymin", "xmax", "ymax"])
    # 标准化submission的呈现格式，
    label_results.sort_values(["image", "label", "xmin", "ymin"], ascending=[True, True, True, True], inplace=True)
    label_results = label_results[["image", "label", "xmin", "ymin", "xmax", "ymax"]]
    run_status_results = pd.concat(run_status_results)
    # run_status_results 加入超时未处理的图片信息
    images = pd.DataFrame(image_ids, columns=["image"])
    run_status_results = pd.merge(images, run_status_results, how="left", on="image")
    run_status_results.loc[run_status_results.status.isnull(), "context"] = "timeout over limit {} s".format(
        EVAL_RUNTIME_LIMIT)
    run_status_results_describe(run_status_results)
    print("step1 get submission success")
    return True, label_results, run_status_results


# api验证内部接口
def _api_eval(url, image_folder, output_filepath="./submission/submission.csv",
              run_status_filepath="./log/run_status.csv"):
    print("url:{} \nimage_folder:{} \noutput_filepath:{} \nrun_status_filepath:{}".format(url, image_folder,
                                                                                          output_filepath,
                                                                                          run_status_filepath))
    start_time = time.time()
    ret, label_results, run_status_results = get_submission_iter(url, image_folder)
    if ret:
        label_results.to_csv(output_filepath, index=False)
        run_status_results.to_csv(run_status_filepath, index=False)
        print("step2 submission to csv success")
    end_time = time.time()
    time_interval = round(end_time - start_time, 1)
    return ret, time_interval, label_results


# api验证外部接口
def api_eval(url, image_folder, output_folder="./output/", team_id="D2023000", req_retry_cnt=3):
    print("eval main start")
    global REQ_RETRY_CNT
    REQ_RETRY_CNT = req_retry_cnt
    print("team_id:{}, req_retry_cnt:{}".format(team_id, req_retry_cnt))
    try:
        # 输出文件夹处理
        team_folder = os.path.join(output_folder, team_id)
        if not os.path.exists(team_folder):
            os.makedirs(team_folder)
        # ./output/D2023000/submission_D2023000.csv
        output_filepath = os.path.join(output_folder, team_id, "submission_{}.csv".format(team_id))
        # ./output/D2023000/run_status_D2023000.csv
        run_status_filepath = os.path.join(output_folder, team_id, "run_status_{}.csv".format(team_id))

        ret, time_interval, label_results = _api_eval(url, image_folder, output_filepath, run_status_filepath)
    except Exception as e:
        print("_api_eval fail")
        print(str(e))
        print(str(traceback.format_exc()))
        ret, time_interval, label_results = (False, 0, None)

    if ret:
        print("eval main stop success")
        print("time_interval: {} seconds".format(time_interval))
    else:
        print("eval main stop fail")

    return ret,time_interval


# 运行时长限制，默认20分钟
EVAL_RUNTIME_LIMIT = 20 * 60
REQ_RETRY_CNT = 3
if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("-url", dest="url", default='http://156.225.17.67:28000/AI/competition', type=str)
    args = parser.parse_args()
    url = args.url

    image_folder = "./data/test_images"
    # image_folder = "./data/eval_images_s1"
    team_id = "D2023000"
    api_eval(url, image_folder, team_id=team_id)