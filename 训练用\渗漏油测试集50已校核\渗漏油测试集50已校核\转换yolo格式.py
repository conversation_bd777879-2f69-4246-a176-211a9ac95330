#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XML标注文件转换为YOLO格式
将XML格式的目标检测标注转换为YOLO训练所需的txt格式
"""

import os
import xml.etree.ElementTree as ET
from pathlib import Path


class XMLToYOLO:
    def __init__(self, xml_dir, output_dir, class_names=None):
        """
        初始化转换器
        
        Args:
            xml_dir (str): XML标注文件目录
            output_dir (str): YOLO格式输出目录
            class_names (list): 类别名称列表，如果为None则自动从XML中提取
        """
        self.xml_dir = Path(xml_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 如果没有提供类别名称，则自动提取
        if class_names is None:
            self.class_names = self._extract_class_names()
        else:
            self.class_names = class_names
            
        # 创建类别名称到索引的映射
        self.class_to_idx = {name: idx for idx, name in enumerate(self.class_names)}
        
        print(f"检测到的类别: {self.class_names}")
        print(f"类别映射: {self.class_to_idx}")
    
    def _extract_class_names(self):
        """从所有XML文件中提取类别名称"""
        class_names = set()
        
        for xml_file in self.xml_dir.glob("*.xml"):
            try:
                tree = ET.parse(xml_file)
                root = tree.getroot()
                
                for obj in root.findall('object'):
                    name_elem = obj.find('name')  # 根据示例XML，类别名称在<n>标签中
                    if name_elem is not None:
                        class_names.add(name_elem.text)
            except Exception as e:
                print(f"解析文件 {xml_file} 时出错: {e}")
        
        return sorted(list(class_names))
    
    def convert_bbox_to_yolo(self, bbox, img_width, img_height):
        """
        将边界框坐标转换为YOLO格式
        
        Args:
            bbox (dict): 包含xmin, ymin, xmax, ymax的字典
            img_width (int): 图像宽度
            img_height (int): 图像高度
            
        Returns:
            tuple: (x_center, y_center, width, height) 归一化后的坐标
        """
        xmin, ymin, xmax, ymax = bbox['xmin'], bbox['ymin'], bbox['xmax'], bbox['ymax']
        
        # 计算中心点坐标
        x_center = (xmin + xmax) / 2.0
        y_center = (ymin + ymax) / 2.0
        
        # 计算宽度和高度
        width = xmax - xmin
        height = ymax - ymin
        
        # 归一化到[0,1]
        x_center /= img_width
        y_center /= img_height
        width /= img_width
        height /= img_height
        
        return x_center, y_center, width, height
    
    def convert_single_xml(self, xml_file):
        """
        转换单个XML文件为YOLO格式
        
        Args:
            xml_file (Path): XML文件路径
            
        Returns:
            bool: 转换是否成功
        """
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            # 获取图像尺寸
            size_elem = root.find('size')
            if size_elem is None:
                print(f"警告: {xml_file} 中没有找到size信息")
                return False
                
            img_width = int(size_elem.find('width').text)
            img_height = int(size_elem.find('height').text)
            
            # 准备YOLO格式的标注
            yolo_annotations = []
            
            # 处理每个目标对象
            for obj in root.findall('object'):
                # 获取类别名称
                name_elem = obj.find('name')
                if name_elem is None:
                    print(f"警告: {xml_file} 中的对象没有类别名称")
                    continue
                    
                class_name = name_elem.text
                if class_name not in self.class_to_idx:
                    print(f"警告: 未知类别 '{class_name}' 在文件 {xml_file}")
                    continue
                
                class_idx = self.class_to_idx[class_name]
                
                # 获取边界框坐标
                bndbox = obj.find('bndbox')
                if bndbox is None:
                    print(f"警告: {xml_file} 中的对象没有边界框信息")
                    continue
                
                bbox = {
                    'xmin': float(bndbox.find('xmin').text),
                    'ymin': float(bndbox.find('ymin').text),
                    'xmax': float(bndbox.find('xmax').text),
                    'ymax': float(bndbox.find('ymax').text)
                }
                
                # 转换为YOLO格式
                x_center, y_center, width, height = self.convert_bbox_to_yolo(
                    bbox, img_width, img_height
                )
                
                # 添加到标注列表
                yolo_annotations.append(f"{class_idx} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}")
            
            # 保存YOLO格式文件
            output_file = self.output_dir / f"{xml_file.stem}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(yolo_annotations))
            
            print(f"转换完成: {xml_file.name} -> {output_file.name}")
            return True
            
        except Exception as e:
            print(f"转换文件 {xml_file} 时出错: {e}")
            return False
    
    def convert_all(self):
        """转换所有XML文件"""
        xml_files = list(self.xml_dir.glob("*.xml"))
        
        if not xml_files:
            print(f"在目录 {self.xml_dir} 中没有找到XML文件")
            return
        
        print(f"开始转换 {len(xml_files)} 个XML文件...")
        
        success_count = 0
        for xml_file in xml_files:
            if self.convert_single_xml(xml_file):
                success_count += 1
        
        print(f"\n转换完成! 成功转换 {success_count}/{len(xml_files)} 个文件")
        
        # 保存类别名称文件
        classes_file = self.output_dir / "classes.txt"
        with open(classes_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(self.class_names))
        print(f"类别文件已保存: {classes_file}")


def main():
    """主函数"""
    # 设置路径
    xml_dir = "Annotations"  # XML标注文件目录
    output_dir = "yolo_labels"  # YOLO格式输出目录
    
    # 创建转换器并执行转换
    converter = XMLToYOLO(xml_dir, output_dir)
    converter.convert_all()


if __name__ == "__main__":
    main()
