# 使用说明

## 1.环境部署

##### 第一步：安装python3.9

1.1 先安装ananconda软件，官网地址：https://www.anaconda.com/download。

1.2 安装完成后，在conda命令窗口，使用命令 `conda create -n py39 python=3.9` 创建3.9的虚拟环境。

1.3 然后激活虚拟环境 `conda activate py39` ，然后再进入项目文件夹，进行第二步依赖库的安装。

##### 第二步：安装依赖库

方法1：直接运行installPackages.py一键安装依赖库的脚本。命令为：

`python installPackages.py`

方法2：运行下方命令：
`pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple`

## 2.文件结构

**启动_命令行赛题版.py**：命令行版本的启动文件，可生成submission.csv以及带有标注框的图形文件。

**results**：命令行版专属，结果文件夹，程序运行后，带有标注框的图形文件保存在此文件夹下。

**submission.csv**：赛题的提交结果文件。

**启动_图形界面版.py**：图形界面版的启动文件，可检测图片，视频，摄像头中的目标。

**datasets**：数据集。

**Font**：字体文件。

**models**：模型权重文件。

**runs**：程序运行过程中生成的中间文件。

**save_data**：图形界面版专属，如果点击保存，则会将带有标注框的图形文件保存在此路径下。

**Test_Imgs**：待检测的图片。

**Test_Videos**：待检测的视频。

**installPackages.py**：一键安装依赖库库的脚本。

其他python文件均为程序运行需要的中间文件。

## 3.使用方法

##### 3.1 命令行版本

3.1.1 把需要预测的图片放在**Test_Imgs**文件夹中。

3.1.2 运行：`python 启动_命令行赛题版.py`

3.1.3 运行后，标注框后的图片保存在result文件夹下，submission.csv保存在**根目录**下。

##### 3.2 GUI可视化版本

运行：`python 启动_图形界面版.py`
