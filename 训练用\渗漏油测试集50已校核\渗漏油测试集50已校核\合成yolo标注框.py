#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO标注框可视化工具
在图像上绘制YOLO格式的标注框，用于验证标注的正确性
"""

import os
import cv2
import numpy as np
from pathlib import Path
import random
import colorsys


class YOLOVisualizer:
    def __init__(self, images_dir, labels_dir, classes_file=None, output_dir=None):
        """
        初始化可视化工具
        
        Args:
            images_dir (str): 图像文件目录
            labels_dir (str): YOLO标注文件目录
            classes_file (str): 类别名称文件路径
            output_dir (str): 输出目录，如果为None则显示图像而不保存
        """
        self.images_dir = Path(images_dir)
        self.labels_dir = Path(labels_dir)
        self.output_dir = Path(output_dir) if output_dir else None
        
        if self.output_dir:
            self.output_dir.mkdir(exist_ok=True)
        
        # 加载类别名称
        self.class_names = self._load_class_names(classes_file)
        
        # 生成颜色
        self.colors = self._generate_colors(len(self.class_names))
        
        print(f"类别数量: {len(self.class_names)}")
        print(f"类别名称: {self.class_names}")
    
    def _load_class_names(self, classes_file):
        """加载类别名称"""
        if classes_file and Path(classes_file).exists():
            with open(classes_file, 'r', encoding='utf-8') as f:
                return [line.strip() for line in f.readlines() if line.strip()]
        else:
            # 如果没有类别文件，使用默认名称
            return ['sly']  # 根据XML示例，类别名称是'sly'
    
    def _generate_colors(self, num_classes):
        """生成不同的颜色用于不同类别"""
        colors = []
        for i in range(num_classes):
            hue = i / num_classes
            saturation = 0.8
            value = 0.9
            rgb = colorsys.hsv_to_rgb(hue, saturation, value)
            # 转换为BGR格式（OpenCV使用BGR）
            bgr = (int(rgb[2] * 255), int(rgb[1] * 255), int(rgb[0] * 255))
            colors.append(bgr)
        return colors
    
    def yolo_to_bbox(self, yolo_coords, img_width, img_height):
        """
        将YOLO格式坐标转换为边界框坐标
        
        Args:
            yolo_coords (tuple): (x_center, y_center, width, height) 归一化坐标
            img_width (int): 图像宽度
            img_height (int): 图像高度
            
        Returns:
            tuple: (xmin, ymin, xmax, ymax) 像素坐标
        """
        x_center, y_center, width, height = yolo_coords
        
        # 转换为像素坐标
        x_center *= img_width
        y_center *= img_height
        width *= img_width
        height *= img_height
        
        # 计算边界框坐标
        xmin = int(x_center - width / 2)
        ymin = int(y_center - height / 2)
        xmax = int(x_center + width / 2)
        ymax = int(y_center + height / 2)
        
        return xmin, ymin, xmax, ymax
    
    def draw_bbox(self, image, bbox, class_idx, confidence=None):
        """
        在图像上绘制边界框
        
        Args:
            image (np.ndarray): 图像数组
            bbox (tuple): (xmin, ymin, xmax, ymax) 边界框坐标
            class_idx (int): 类别索引
            confidence (float): 置信度（可选）
        """
        xmin, ymin, xmax, ymax = bbox
        
        # 获取类别名称和颜色
        class_name = self.class_names[class_idx] if class_idx < len(self.class_names) else f"class_{class_idx}"
        color = self.colors[class_idx % len(self.colors)]
        
        # 绘制边界框
        cv2.rectangle(image, (xmin, ymin), (xmax, ymax), color, 2)
        
        # 准备标签文本
        if confidence is not None:
            label = f"{class_name}: {confidence:.2f}"
        else:
            label = class_name
        
        # 计算文本尺寸
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
        
        # 绘制标签背景
        cv2.rectangle(image, 
                     (xmin, ymin - text_height - baseline - 5), 
                     (xmin + text_width, ymin), 
                     color, -1)
        
        # 绘制标签文本
        cv2.putText(image, label, 
                   (xmin, ymin - baseline - 2), 
                   font, font_scale, (255, 255, 255), thickness)
    
    def visualize_single_image(self, image_path, save_result=True):
        """
        可视化单张图像的标注

        Args:
            image_path (Path): 图像文件路径
            save_result (bool): 是否保存结果

        Returns:
            np.ndarray: 绘制了标注框的图像
        """
        # 读取图像 - 使用cv2.imdecode处理中文路径
        try:
            # 使用numpy读取文件，然后用cv2解码
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_array = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

            if image is None:
                print(f"无法解码图像: {image_path}")
                return None
        except Exception as e:
            print(f"读取图像时出错 {image_path}: {e}")
            return None
        
        img_height, img_width = image.shape[:2]
        
        # 查找对应的标注文件
        label_file = self.labels_dir / f"{image_path.stem}.txt"
        
        if not label_file.exists():
            print(f"标注文件不存在: {label_file}")
            return image
        
        # 读取标注
        try:
            with open(label_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) < 5:
                    print(f"标注格式错误: {line}")
                    continue
                
                class_idx = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # 如果有置信度信息
                confidence = float(parts[5]) if len(parts) > 5 else None
                
                # 转换坐标
                bbox = self.yolo_to_bbox((x_center, y_center, width, height), img_width, img_height)
                
                # 绘制边界框
                self.draw_bbox(image, bbox, class_idx, confidence)
        
        except Exception as e:
            print(f"处理标注文件 {label_file} 时出错: {e}")
        
        # 保存或显示结果
        if save_result and self.output_dir:
            output_path = self.output_dir / f"annotated_{image_path.name}"
            # 使用cv2.imencode处理中文路径
            try:
                success, encoded_image = cv2.imencode('.jpg', image)
                if success:
                    with open(output_path, 'wb') as f:
                        f.write(encoded_image.tobytes())
                    print(f"已保存: {output_path}")
                else:
                    print(f"编码图像失败: {output_path}")
            except Exception as e:
                print(f"保存图像时出错 {output_path}: {e}")
        
        return image
    
    def visualize_all_images(self, show_images=False, max_display=10):
        """
        可视化所有图像的标注
        
        Args:
            show_images (bool): 是否显示图像窗口
            max_display (int): 最大显示数量（仅在show_images=True时有效）
        """
        # 支持的图像格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 查找所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(self.images_dir.glob(f"*{ext}"))
            image_files.extend(self.images_dir.glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"在目录 {self.images_dir} 中没有找到图像文件")
            return
        
        print(f"找到 {len(image_files)} 个图像文件")
        
        display_count = 0
        for image_file in image_files:
            print(f"处理: {image_file.name}")
            
            # 可视化图像
            annotated_image = self.visualize_single_image(image_file)
            
            # 显示图像（如果需要）
            if show_images and annotated_image is not None and display_count < max_display:
                cv2.imshow(f"Annotated: {image_file.name}", annotated_image)
                
                print("按任意键继续，按 'q' 退出...")
                key = cv2.waitKey(0) & 0xFF
                cv2.destroyAllWindows()
                
                if key == ord('q'):
                    break
                
                display_count += 1
        
        print("处理完成!")


def main():
    """主函数"""
    # 设置路径
    images_dir = "Images"  # 图像目录
    labels_dir = "yolo_labels"  # YOLO标注目录
    classes_file = "yolo_labels/classes.txt"  # 类别文件
    output_dir = "annotated_images"  # 输出目录
    
    # 创建可视化工具
    visualizer = YOLOVisualizer(images_dir, labels_dir, classes_file, output_dir)
    
    # 可视化所有图像
    visualizer.visualize_all_images(show_images=False)  # 设置为True可以显示图像窗口
    
    print(f"\n标注可视化完成！结果保存在 {output_dir} 目录中")


if __name__ == "__main__":
    main()
